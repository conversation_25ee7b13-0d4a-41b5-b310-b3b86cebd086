<!DOCTYPE html>
<html>
<head>
  <title>Upwork Monitor</title>
  <meta charset="utf-8">
  <style>
    :root {
      /* Upwork Core Colors */
      --up-green: #14a800;
      --up-blue: #086add; /* Primary link color */
      --up-black: #181818; /* Primary text color */
      --up-white: #fff;

      /* Grays */
      --up-gray-10: #1a1a1a; /* Darker text */
      --up-gray-50: #8d8c8c; /* Muted text */
      --up-gray-70: #d9d9d9; /* Borders, lines */
      --up-gray-80: #e9e9e9; /* Light borders, dividers */
      --up-gray-95: #f9f9f9; /* Light backgrounds */

      /* Typography */
      --up-font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; /* Common stack, Upwork uses "Neue Montreal" which may need import */
      --up-font-weight-normal: 400;
      --up-font-weight-medium: 500;
      --up-font-weight-bold: 700;

      /* UI Elements */
      --up-button-bg: var(--up-green);
      --up-button-text: var(--up-white);
      --up-link-color: var(--up-blue);
      --up-light-green-bg: hsl(113, 70%, 97%); /* Very light green for page background */
      --up-link-color-hover: hsl(212, 95%, 43%); /* Darken blue slightly for hover */
    }

    body { font-family: sans-serif; width: 415px; padding: 10px; font-size: 14px; }
    h1 { font-size: 16px; margin: 0 0 8px 0; display: flex; align-items: center; justify-content: space-between; }
    p { margin: 4px 0; } /* Slightly reduced paragraph margin */
    input[type="text"]#userQueryInput {
      flex-grow: 1; /* Allow input to take available space */
      padding: 4px 6px; /* Standard padding */
      margin-bottom: 0; /* Handled by query-section */
      box-sizing: border-box;
    }
    button { margin-top: 4px; padding: 5px 8px; } /* Reduced top margin */
    .query-section {
      margin-bottom: 8px;
      display: flex; /* Align input and button side-by-side */
      align-items: center; /* Vertically align items in the middle */
    }
    .status-section p { margin-bottom: 4px; font-size: 0.9em; color: #333; } /* Style for the consolidated status */
    hr { margin: 8px 0; border: 0; border-top: 1px solid var(--up-gray-80); } /* Reduced margin, styled HR */
    .recent-jobs-container {
      margin-top: 10px;
      max-height: 400px; /* Default max-height when jobs are present */
      overflow-y: auto;
      transition: max-height 0.3s ease-in-out; /* Optional: smooth transition */
    }
    .recent-jobs-container.empty-list {
      max-height: 60px; /* Max-height when the list is empty */
    }

    body {
      font-family: var(--up-font-family);
      color: var(--up-black);
      background-color: var(--up-light-green-bg);
    }
    h1, h2, h3, h4, h5, h6 {
      font-weight: var(--up-font-weight-medium);
    }

    .job-item {
      border: 1px solid var(--up-gray-70); /* Adjusted for more contrast */
      border-left: 3px solid var(--up-gray-70); /* Default left border for all items */
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 8px;
      background-color: var(--up-gray-95); /* Default background for all items */
      font-size: 13px;
    }
    .job-item h3  {
      margin: 0; /* Reset margin for h3 */
      font-size: 14px; /* Keep font size */
      /* font-weight will be controlled by .job-title-collapsed h3 */
      flex-grow: 1; /* Allow title to take up remaining space */
      min-width: 0; /* IMPORTANT: Allows h3 to shrink and text-overflow on child <a> to work */
      line-height: 1.3; /* Improves readability */
      /* Make h3 a flex container for the link and the applied icon */
      display: flex;
      align-items: center;
    }
    .job-item h3 a { /* Style for the job title link */
      /* display: block; /* No longer needed if h3 is flex and 'a' is a flex item */
      /* width: 353px; /* REMOVE fixed width, let flexbox handle it */
      white-space: nowrap; /* Prevent wrapping */
      overflow: hidden; /* Hide overflowing text */
      text-overflow: ellipsis; /* Show ellipsis for truncated text */
      color: var(--up-link-color);
      text-decoration: none; /* Ensure default link decoration is set here */
      /* For ellipsis to work when 'a' is a flex item inside an h3 (which is also a flex item) */
      min-width: 0; /* Allow the link to shrink and show ellipsis */
    }
    .job-item a  {
     text-decoration:  none;
     color:  var(--up-link-color);
    }
    .job-item a:hover  {
     text-decoration:  underline;
     color: var(--up-link-color-hover);
    }
    .job-item p  {
     margin:  3px 0;
     color:  var(--up-gray-10); /* Darker gray for paragraph text */
    }
    .job-item .skills  {
     font-style:  italic;
     font-size:  12px;
     color:  var(--up-gray-50); /* Muted gray for skills */
    }
    /* Slightly smaller font,  moved out of .job-item */
    .no-jobs  {
     text-align:  center;
     color:  #777;
     padding:  10px;
     font-size:  13px;
    }
    /* New class for collapsed job titles */
    .job-title-collapsed h3 {
        font-weight: var(--up-font-weight-normal);
    }

    .low-priority-prefix {
      display: inline-block; /* Important for padding and background to work well */
      background-color: var(--up-gray-80); /* A light gray background, adjust as needed */
      color: var(--up-gray-10); /* Darker text for contrast */
      padding: 2px 6px;      /* Small padding around the text */
      border-radius: 4px;    /* Rounded corners */
      font-size: 0.9em;     /* Slightly smaller font size */
      font-style: normal;    /* Remove italics if previously set, or keep if preferred */
      font-weight: var(--up-font-weight-normal); /* Ensure it's not bold */
      margin-right: 6px;     /* Space between the tag and the job title */
      line-height: 1.2;      /* Adjust line-height for better vertical alignment if needed */
      vertical-align: middle; /* Helps align with the text if font sizes differ significantly */
    }


    .job-header {
      display: flex;
      align-items: center;
    }
    .toggle-details {
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      user-select: none; /* Prevent text selection */
      text-align: center;
      line-height: 1; /* Adjust for better vertical alignment with title */
      flex-shrink: 0; /* Prevent toggle from shrinking */
      width: 15px;    /* Fixed width for the toggle button */
      margin-right: 5px; /* Space between toggle and title */
      /* Align toggle and delete button vertically */
      align-self: flex-start;
      margin-top: 2px; /* Small adjustment for vertical alignment */
    }
    .delete-job-button {
      font-size: 14px; /* Smaller size than toggle */
      cursor: pointer;
      color: var(--up-gray-50); /* Muted color */
      margin-left: auto; /* Push button to the right */
      padding: 0 0 0 8px; /* Left padding for spacing */
      padding: 0; /* Reset padding, use margin for spacing */
    }
    .applied-job-icon {
      margin: 3px 8px 0 0; /* Space between title and icon */
      display: inline-flex; /* To align SVG properly */
      align-items: center;
    }
    .applied-job-icon .air3-icon svg {
      width: 16px; /* Adjust size as needed */
      height: 16px; /* Adjust size as needed */
      --icon-color: var(--up-blue); /* Use Upwork blue variable */
    }
    .job-details {
      padding-left: 20px; /* Indent details under the toggle button's original space */
      padding-top: 5px; /* Space between header and details */
    }
    .job-item.excluded-by-filter {
      padding: 4px 8px;
      /* Now inherits base background and border-left */
      /* Optionally, make it slightly different if needed, e.g., a darker border */
      border-left-color: var(--up-gray-80); /* Make excluded items' left border slightly ligher */
    }
    .job-item.excluded-by-filter .job-header h3 a {
      color: var(--up-gray-50); /* Slightly muted title color for excluded items */
      font-size: .9em; /* Slightly smaller font size for excluded items */
    }
    #manualCheckButton {
      background: none;
      border: none;
      font-size: 18px; /* Adjust size of icon */
      padding: 0 5px;
      margin: 0 0 0 10px; /* Margin to space it from title */
      cursor: pointer;
      color: var(--up-gray-10);
    }
    #manualCheckButton:hover { color: var(--up-link-color); }
    #saveQueryButton {
      background: transparent;
      border: none;
      color: var(--up-gray-50); /* Muted icon color */
      padding: 0 0 0 2px; /* Minimal padding for click target */
      font-size: 18px; /* Slightly larger icon for better clickability */
      line-height: 1; /* Helps align icon vertically */
      cursor: pointer;
      margin-left: 0; /* Space between input and button */
      margin-top: 0; /* Reset default button margin-top */
      /* Ensure vertical alignment if line-height isn't enough */
      display: inline-flex;
      align-items: center;
    }
    #saveQueryButton:hover { color: var(--up-black); /* Darken icon on hover */ }
    .high-rating {
      border-left-color: var(--up-green) !important;
      background-color: hsl(113, 70%, 95%) !important; /* Lighter green */
    }
    .job-item.job-applied {
      border-left-color: var(--up-blue) !important;
      background-color: hsl(212, 90%, 95%) !important; /* Lighter blue */
      opacity: 0.7; /* Make them slightly faded */
    }
    .job-item.low-priority {
      /* Reduce vertical padding to take less space, especially when collapsed */
      padding-top: 4px;
      padding-bottom: 4px;
    }
    .job-item.low-priority h3 {
      font-size: 0.85em;     /* Slightly smaller font size */
    }
    .job-item.low-priority h3 a {
      color: var(--up-black); /* Neutral text color */
      text-decoration: none;  /* Remove underline */
    }
    .job-item.low-priority h3 a:hover {
      text-decoration: none;  /* Ensure no underline on hover */
      color: var(--up-gray-10); /* Slightly different neutral for hover */
    }
    

    .unverified-icon {
      color: var(--orange-light, #cc9b66); /* Using a color from your list, or fallback */
      margin-left: 4px;
      cursor: help;
      font-size: 1.1em;
    }
  </style>
</head>
<body>
  <h1><a id="popupTitleLink" href="#" target="_blank">Upwork Job Monitor (MV2)</a> <button id="manualCheckButton" title="Check Now (with saved query)">&#x21BB;</button></h1>

  <div class="status-section">
    <p id="consolidatedStatus">Initializing status...</p>
  </div>
  <hr>
  <div class="query-section">
    <input type="text" id="userQueryInput" placeholder="Enter Upwork search query...">
    <button id="saveQueryButton" title="Save & Check" aria-label="Save and Check">&#x1F50D;</button> <!-- Search icon -->
  </div>
  <hr>
  <!--h2 style="font-size: medium">Recently Found Jobs:</!--h2 -->
  <div id="recentJobsList" class="recent-jobs-container">
    <!-- Jobs will be injected here -->
    <p class="no-jobs">No new jobs found in the last check.</p>
  </div>

  <script src="../lib/browser-polyfill.min.js"></script>
  <script src="../utils.js"></script> <!-- Add this line -->
  <script src="../storage/storage-manager.js"></script> <!-- Add this line -->
  <script src="popup.js"></script>
</body>
</html>
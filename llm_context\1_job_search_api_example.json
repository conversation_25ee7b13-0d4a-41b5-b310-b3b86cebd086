{"request": {"endpoint": "https://www.upwork.com/api/graphql/v1?alias=userJobSearch", "payload": {"query": "\n  query UserJobSearch($requestVariables: UserJobSearchV1Request!) {\n    search {\n      universalSearchNuxt {\n        userJobSearchV1(request: $requestVariables) {\n          paging {\n            total\n            offset\n            count\n          }\n          facets {\n            jobType {\n              key\n              value\n            }\n            workload {\n              key\n              value\n            }\n            clientHires {\n              key\n              value\n            }\n            durationV3 {\n              key\n              value\n            }\n            amount {\n              key\n              value\n            }\n            contractorTier {\n              key\n              value\n            }\n            contractToHire {\n              key\n              value\n            }\n            paymentVerified: payment {\n              key\n              value\n            }\n            proposals {\n              key\n              value\n            }\n            previousClients {\n              key\n              value\n            }\n          }\n          results {\n            id\n            title\n            description\n            relevanceEncoded\n            ontologySkills {\n              uid\n              parentSkillUid\n              prefLabel\n              prettyName: prefLabel\n              freeText\n              highlighted\n            }\n            isSTSVectorSearchResult\n            connectPrice\n            applied\n            upworkHistoryData {\n              client {\n                paymentVerificationStatus\n                country\n                totalReviews\n                totalFeedback\n                hasFinancialPrivacy\n                totalSpent {\n                  isoCurrencyCode\n                  amount\n                }\n              }\n              freelancerClientRelation {\n                lastContractRid\n                companyName\n                lastContractTitle\n              }\n            }\n            jobTile {\n              job {\n                id\n                ciphertext: cipherText\n                jobType\n                weeklyRetainerBudget\n                hourlyBudgetMax\n                hourlyBudgetMin\n                hourlyEngagementType\n                contractorTier\n                sourcingTimestamp\n                createTime\n                publishTime\n                enterpriseJob\n                personsToHire\n                premium\n                totalApplicants\n                hourlyEngagementDuration {\n                  rid\n                  label\n                  weeks\n                  mtime\n                  ctime\n                }\n                fixedPriceAmount {\n                  isoCurrencyCode\n                  amount\n                }\n                fixedPriceEngagementDuration {\n                  id\n                  rid\n                  label\n                  weeks\n                  ctime\n                  mtime\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  ", "variables": {"requestVariables": {"contractorTier": ["IntermediateLevel", "ExpertLevel"], "userQuery": "NOT \"react\" NOT \"next.js\" \"CLS\" OR \"INP\" OR \"LCP\" OR \"pagespeed\"", "sort": "recency", "highlight": true, "paging": {"offset": 0, "count": 1}}}}}, "response": {"data": {"search": {"universalSearchNuxt": {"userJobSearchV1": {"paging": {"total": 1002, "offset": 0, "count": 1}, "facets": {"jobType": [{"key": "fixed", "value": 366}, {"key": "hourly", "value": 636}], "paymentVerified": [{"key": "verified", "value": 924}]}, "results": [{"id": "1934324853507450527", "title": "Mobile Site Speed Optimization Expert Needed", "description": "We are seeking an experienced individual to enhance the loading speed of our website, particularly on mobile devices. Please break down specifically what tasks you do and if you will install any plugins on this site as it is on wordpress and elementor. Site is arizonablindscompany.com", "upworkHistoryData": {"client": {"paymentVerificationStatus": "VERIFIED", "country": "United States", "totalReviews": 58, "totalFeedback": 4.78, "hasFinancialPrivacy": false, "totalSpent": {"isoCurrencyCode": "USD", "amount": "8098.49"}}}, "jobTile": {"job": {"id": "1934324853507450527", "ciphertext": "~021934324853507450527", "jobType": "FIXED", "contractorTier": "IntermediateLevel", "publishTime": "2025-06-15T18:59:18.224Z", "totalApplicants": 13, "fixedPriceAmount": {"isoCurrencyCode": null, "amount": "75.0"}}}}]}}}}}}
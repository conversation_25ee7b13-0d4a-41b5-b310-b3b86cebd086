:root {
  /* Upwork Core Colors from air-3-0 theme */
  --up-green: #14a800; /* Used for hover states, success indicators */
  --brand-green: #108a00; /* Primary action color, e.g., buttons */
  --up-blue: #086add; /* Primary link color */
  --up-blue-hover: hsl(212, 95%, 38%); /* Darker blue for link hover */
  --up-black: #181818; /* Primary text color */
  --white: #fff;

  /* Grays from Upwork Palette */
  --up-gray-10: #1a1a1a; /* Darker text */
  --up-gray-50: #8d8c8c; /* Muted text */
  --up-gray-70: #d9d9d9; /* Borders, lines */
  --up-gray-80: #e9e9e9; /* Light borders, dividers */
  --up-gray-95: #f9f9f9; /* Light backgrounds */

  /* Semantic Colors from Upwork Palette */
  --orange-light: #cc9b66; /* For warnings or neutral indicators */

  /* Typography */
  --up-font-family: "Neue Montreal", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --up-font-weight-normal: 400;
  --up-font-weight-medium: 500;
  --up-font-weight-bold: 700;

  /* UI Elements */
  --up-button-bg: var(--brand-green);
  --up-button-bg-hover: var(--up-green);
  --up-button-text: var(--white);
  --up-link-color: var(--up-blue);
  --up-link-color-hover: var(--up-blue-hover);
  /*--up-page-bg: #effcef;*/ /* --green-lightest from Upwork */
  /*--up-job-item-bg-high-rating: #effcef;*/ /* --green-lightest from Upwork */
  --up-page-bg: #f1fdf0; /* A very light green, almost white */
  --up-job-item-bg-high-rating: #e6f4e4; /* A slightly more saturated light green to stand out */
   --up-job-item-bg-applied: #f3f8fe; /* --blue-lightest from Upwork */
}

@font-face {
  font-family: 'Neue Montreal';
  src: url('neue-montreal-variable.latin.woff2') format('woff2');
  font-weight: 200 800;
  font-style: normal;
  font-display: swap;
}

body {
  font-family: var(--up-font-family, sans-serif); /* Use CSS var, fallback to sans-serif */
  width: 700px; padding: 10px; font-size: 14px; overflow-x: hidden;
  color: var(--up-black);
  background-color: var(--up-page-bg);
  margin: 0; /* Reset default body margin */
  padding: 12px;
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}
.app-header {
  font-size: 16px;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.app-header__title {
  /* Inherits general 'a' styles */
}
.app-header__meta {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.app-header__status {
  font-size: 0.9em;
  margin: 0 10px 0 0; /* Remove bottom margin, add right margin for spacing */
  font-weight: var(--up-font-weight-normal); /* Ensure it's not bold like h1 */
  display: flex;
  gap: 6px;
  align-items: center;
}
.app-header__status-tag {
  background-color: var(--up-gray-80);
  color: var(--up-gray-10);
  padding: 3px 8px;
  border-radius: 5px; /* Pill shape */
  font-size: 0.8em; /* Relative to parent .app-header__status */
  line-height: 1.3;
  white-space: nowrap;
}
.app-header__button {
  margin: 0 0 0 10px;
}

p { margin: 4px 0; }
hr { margin: 8px 0; border: 0; border-top: 1px solid var(--up-gray-80); }



/* Base button styles */
.btn {
  /* Define local custom properties for the button component API */
  --btn-bg-color: transparent;
  --btn-bg-color-hover: transparent;
  --btn-color: var(--up-gray-10);
  --btn-color-hover: var(--up-link-color);

  background-color: var(--btn-bg-color);
  color: var(--btn-color);

  margin: 0; /* Normalize button margins */
  border: none; /* Remove default button border */
  border-radius: 4px; /* Add slight border-radius for consistency */
  cursor: pointer;
  transition: background-color 0.1s ease-in-out, color 0.1s ease-in-out; /* Smooth transition for hover effect */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn:hover {
  background-color: var(--btn-bg-color-hover);
  color: var(--btn-color-hover);
}

/* Primary button style (green background) */
.btn--primary {
  /* Modifier only sets the custom properties */
  --btn-bg-color: var(--up-button-bg);
  --btn-bg-color-hover: var(--up-button-bg-hover);
  --btn-color: var(--up-button-text);
  --btn-color-hover: var(--up-button-text);

  margin-top: 4px;
  padding: 5px 8px;
}

/* Icon button base style */
.btn--icon {
  font-size: 18px; /* Default icon size */
  padding: 0 5px; /* Minimal padding for click target */
  line-height: 1; /* Helps align icon vertically */
}

/* Muted color variations for icon buttons */
.btn--muted {
  --btn-color: var(--up-gray-50);
  --btn-color-hover: var(--up-black);
}


.query-section {
  margin-bottom: 8px;
  display: flex; /* Align input and button side-by-side */
  align-items: center; /* Vertically align items in the middle */
}
.query-section__input {
  flex-grow: 1; /* Allow input to take available space */
  box-sizing: border-box;
}
.query-section__button {
  padding: 0 0 0 5px;
}
/*.query-section__button::after {
    content: "\26B2";
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    display: block;
    font-size: 1.3em;
    font-weight: 700;
    color: var(--up-green);
    text-shadow: 0 0 16px #14a800;
}*/

/* --- BEM Block: job-item --- */
.job-item {
  position: relative;
  border: 1px solid var(--up-gray-70);
  border-left: 3px solid var(--up-gray-70);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  background-color: var(--up-gray-95);
  font-size: 13px;
  transition: background-color 0.2s ease; /* Smooth transition for selection */
  transition: box-shadow 0.2s ease-in-out;
}

.job-item:hover, .job-item--selected {
  box-shadow: inset 0 0 0 1000px rgba(0, 0, 0, 0.04);
}


.job-item:last-child {
    margin-bottom: 0;
}

.job-item__header {
  display: flex;
  align-items: center;
}

.job-item__toggle {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  text-align: center;
  line-height: 1;
  flex-shrink: 0;
  width: 15px;
  margin-right: 5px;
  align-self: flex-start;
  margin-top: 2px;
}

.job-item__title-container {
  margin: 0;
  font-size: 14px;
  flex-grow: 1;
  min-width: 0; /* IMPORTANT: Allows h3 to shrink and text-overflow on child <a> to work */
  line-height: 1.3;
  display: flex;
  align-items: center;
  font-weight: var(--up-font-weight-medium); /* Default font weight */
}

.job-item__applied-icon {
  margin: 3px 8px 0 0;
  display: inline-flex;
  align-items: center;
  width: 16px;
  height: 16px;
  --icon-color: var(--up-blue);
}

.job-item__priority-tag {
  display: inline-block;
  background-color: var(--up-gray-80);
  color: var(--up-gray-10);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  font-style: normal;
  font-weight: var(--up-font-weight-normal);
  margin-right: 6px;
  line-height: 1.2;
  vertical-align: middle;
}

.job-item__title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--up-link-color);
  text-decoration: none;
  min-width: 0; /* Allow the link to shrink and show ellipsis */
}

.job-item__title:hover {
  text-decoration: underline;
  color: var(--up-link-color-hover);
}

.job-item__delete-btn {
  font-size: 14px;
  cursor: pointer;
  color: var(--up-gray-50);
  margin-left: auto;
  padding: 0 0 0 8px;
}

.job-item__details {
  display: block; /* Default state */
  padding-left: 20px;
  padding-top: 5px;
}

.job-item__meta {
  margin: 3px 0;
  color: var(--up-gray-10);
}

.job-item__skills {
  margin: 3px 0;
  font-style: italic;
  font-size: 12px;
  color: var(--up-gray-50);
}

/* --- job-item Modifiers --- */

.job-item--collapsed .job-item__details {
  display: none;
}

.job-item--collapsed .job-item__title-container {
  font-weight: var(--up-font-weight-normal);
}

.job-item--high-rating,
.job-item--high-spent {
  border-left-color: var(--up-green);
  background-color: var(--up-job-item-bg-high-rating);
}
.job-item--applied {
  border-left-color: var(--up-blue);
  background-color: var(--up-job-item-bg-applied);
  opacity: 0.7;
}

/* Modifiers for de-emphasized items (excluded or low priority) */
.job-item--excluded,
.job-item--low-priority {
  padding-top: 4px;
  padding-bottom: 4px;
}
.job-item--excluded .job-item__title-container,
.job-item--low-priority .job-item__title-container {
  font-size: 0.9em;
}
.job-item--excluded {
  border-left-color: var(--up-gray-80);
}
.job-item--excluded .job-item__title {
  color: var(--up-gray-50);
}
.job-item--low-priority .job-item__title {
  color: var(--up-black);
  text-decoration: none;
}
.job-item--low-priority .job-item__title:hover {
  text-decoration: none;
  color: var(--up-gray-10);
}
.job-item--selected {
  /*background-color: var(--up-job-item-bg-applied);*/
  border-left-color: var(--up-blue);
}
.job-item--selected::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid var(--up-blue);
}


/* --- End job-item Block --- */

.job-item__unverified-icon {
  color: var(--orange-light);
  margin-left: 4px;
  cursor: help;
  font-size: 1.1em;
}

.job-item__client-rating--positive {
  color: var(--up-green);
  font-weight: var(--up-font-weight-bold);
}

.job-item__client-spent--positive {
  color: var(--up-green);
  font-weight: var(--up-font-weight-bold);
}

.details-panel__loading,
.details-panel__error {
  color: var(--up-gray-50);
  font-style: italic;
}

.details-panel__error {
  color: #d00; /* A distinct error color */
}

.details-panel__stats-group {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* Reduced gap */
  margin-bottom: 6px; /* Reduced bottom margin */
}

.details-panel__stat {
  background-color: var(--up-gray-90);
  padding: 2px 6px; /* Reduced padding */
  border-radius: 4px;
  font-size: 0.8em; /* Slightly smaller font */
}

.details-panel__bids {
  margin-top: 6px; /* Reduced top margin */
  padding-top: 6px; /* Reduced top padding */
  border-top: 1px solid var(--up-gray-80);
}

.details-panel__questions {
  margin-top: 6px; /* Reduced top margin */
  padding-top: 6px; /* Reduced top padding */
  border-top: 1px solid var(--up-gray-80);
}

.details-panel__description {
  margin-top: 10px; /* Reduced top margin */
  padding-top: 10px; /* Reduced top padding */
  border-top: 1px solid var(--up-gray-80);
}

.details-panel__description-content {
  /*max-height: 280px;*/
  overflow-y: auto;
  /*padding: 8px;*/
  background-color: var(--up-gray-90);
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.5;
  white-space: pre-line; /* Preserve line breaks */
  /*margin-top: 8px;*/
}
.details-panel { /* Styles for the job details panel itself */
  /* Flex properties from old #jobDetailsPanel ID */
  flex-grow: 1; /* Allow this panel to grow and take remaining space */
  flex-shrink: 1; /* Allow to shrink if necessary */
  flex-basis: 0;  /* Start with a basis of 0, flex-grow will expand it */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */

  background-color: var(--white);
  border: 1px solid var(--up-gray-70);
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  overflow-y: auto;
}

.job-list {
  margin-top: 0px;
  /*max-height: 400px;*/ /* Default max-height when jobs are present */
  overflow-y: auto;
  transition: max-height 0.3s ease-in-out; /* Optional: smooth transition */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
  height: 100%; /* Fill the container to enable scrolling within it */
}

.job-list-container {
  position: relative;
  /* Flex properties moved from .job-list to control the container's size in the main layout */
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 385px;
  min-width: 300px;
}
.job-list-container::before,
.job-list-container::after { /* Pseudo-elements for top and bottom scroll hints */
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
}
.job-list-container::before { /* Top shadow */
  top: 0;
  height: 6px;
  background: radial-gradient(ellipse at top, rgba(0, 0, 0, 0.12) 0%, transparent 85%);
  opacity: 0; /* Hidden by default */
}
.job-list-container.job-list-container--scrolled::before {
  opacity: 1; /* Becomes visible when scrolled */
  z-index: 2;
}
.job-list-container::after { /* Bottom shadow */
  bottom: 0;
  height: 6px;
  background: radial-gradient(ellipse at bottom, rgba(0, 0, 0, 0.12) 0%, transparent 85%);
}

.job-list-container.job-list-container--scrolled-to-end::after {
  opacity: 0; /* Hidden when scrolled to the end */
}

.job-list__no-jobs,
.details-panel__no-jobs {
 text-align:  center;
 color:  #777;
 padding:  10px;
 font-size:  13px;
}

.job-list,
.details-panel {
  /* Cross-Browser Scrollbar Styling (Firefox Support) */
  /* Note: Webkit browsers (Chrome, Safari) use ::-webkit-scrollbar */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #ccc transparent; /* Firefox */
}
.job-list::-webkit-scrollbar,
.details-panel::-webkit-scrollbar {
  width: 8px; /* Narrower scrollbar */
}

body::-webkit-scrollbar-thumb,
.job-list::-webkit-scrollbar-thumb,
.details-panel::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}
.arrow-container {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #ccc transparent; /* Firefox */
}

/*.job-list.empty-list {
  max-height: 60px;
}*/
/* Styles for side-by-side layout */
.main-content {
  display: flex;
  flex-direction: row; /* Arrange children side-by-side */
  gap: 15px; /* Space between job list and details panel */
  margin-top: 10px; /* Add some margin at the top */
  height: 471px;
}
.main-content.empty-list {
  height: 90px; /* Adjust height when the job list is empty */
}